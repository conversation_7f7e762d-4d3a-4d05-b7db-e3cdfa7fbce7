"use client";

import { EnhancedFileUpload } from "@/components/EnhancedFileUpload";
import { MainLayout } from "@/components/MainLayout";
import { SEOFaq, pdfMergeFAQs } from "@/components/SEOFaq";
import { SuccessConfetti } from "@/components/SuccessConfetti";
import { Button } from "@/components/ui/button";
import { apiPost, downloadFromResponse } from "@/lib/api";
import { motion } from "framer-motion";
import { useEffect, useState } from "react";
import { toast } from "sonner";

const jsonLd = {
  "@context": "https://schema.org",
  "@type": "WebApplication",
  name: "PDF Merger",
  description:
    "Free online PDF merger tool to combine multiple PDF files into one document",
  url: "https://freefox.com/merge-pdf",
  applicationCategory: "UtilitiesApplication",
  operatingSystem: "Any",
  offers: {
    "@type": "Offer",
    price: "0",
    priceCurrency: "USD",
  },
  featureList: [
    "Combine multiple PDFs",
    "Maintain document quality",
    "No registration required",
    "Secure processing",
  ],
};

export default function MergePDFPage() {
  const [files, setFiles] = useState<File[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [showConfetti, setShowConfetti] = useState(false);

  // Set page metadata on client side
  useEffect(() => {
    document.title = "Free PDF Merger Online - Combine Multiple PDFs | FreeFox";

    // Update meta description
    const metaDescription = document.querySelector('meta[name="description"]');
    if (metaDescription) {
      metaDescription.setAttribute(
        "content",
        "Merge PDF files online for free. Combine multiple PDF documents into one file. No registration required. Fast, secure, and easy to use PDF merger tool."
      );
    }

    // Update canonical URL
    let canonical = document.querySelector('link[rel="canonical"]');
    if (!canonical) {
      canonical = document.createElement("link");
      canonical.setAttribute("rel", "canonical");
      document.head.appendChild(canonical);
    }
    canonical.setAttribute("href", "https://freefox.com/merge-pdf");
  }, []);

  const handleFilesSelected = (selectedFiles: File[]) => {
    setFiles(selectedFiles);
  };

  const handleMerge = async () => {
    if (files.length < 2) {
      toast.error("Please select at least 2 PDF files to merge");
      return;
    }

    setIsProcessing(true);
    setProgress(10);

    try {
      // Simulate chunked upload for large files
      const chunkSize = 1024 * 1024; // 1MB chunks
      const formData = new FormData();

      // Add files to form data with progress simulation
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        formData.append("files", file);

        // Simulate progress for large files
        if (file.size > chunkSize) {
          const chunks = Math.ceil(file.size / chunkSize);
          for (let j = 0; j < chunks; j++) {
            // Simulate chunk upload progress
            await new Promise((resolve) => setTimeout(resolve, 50));
            const fileProgress = (j + 1) / chunks;
            const overallProgress =
              10 + (70 * (i + fileProgress)) / files.length;
            setProgress(Math.min(80, Math.round(overallProgress)));
          }
        }
      }

      setProgress(80);

      // Use the API utility instead of direct fetch
      const response = await apiPost("merge-pdf", formData);

      setProgress(90);

      // Use the utility function to handle the download
      await downloadFromResponse(response, "merged.pdf");

      setProgress(100);
      setShowConfetti(true);
      toast.success("PDF files merged successfully");
    } catch (error) {
      console.error("Error merging PDF files:", error);
      toast.error("Failed to merge PDF files. Please try again.");
    } finally {
      setTimeout(() => {
        setIsProcessing(false);
        setProgress(0);
      }, 500);
    }
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { type: "spring", stiffness: 300, damping: 24 },
    },
  };

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />
      <MainLayout>
        <SuccessConfetti
          show={showConfetti}
          duration={3000}
          onComplete={() => setShowConfetti(false)}
        />

        <motion.div
          className="max-w-3xl mx-auto"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <motion.div className="mb-8 space-y-4" variants={itemVariants}>
            <div className="space-y-2">
              <h1 className="text-4xl font-bold tracking-tight">
                Free PDF Merger Online
              </h1>
              <p className="text-xl text-muted-foreground">
                Combine multiple PDF files into one document. No registration
                required.
              </p>
            </div>
            <div className="flex flex-wrap gap-2 text-sm text-muted-foreground">
              <span className="bg-primary/10 px-2 py-1 rounded">
                ✓ Unlimited Files
              </span>
              <span className="bg-primary/10 px-2 py-1 rounded">
                ✓ Preserve Quality
              </span>
              <span className="bg-primary/10 px-2 py-1 rounded">
                ✓ Secure Processing
              </span>
              <span className="bg-primary/10 px-2 py-1 rounded">
                ✓ Instant Download
              </span>
            </div>
          </motion.div>

          <motion.div className="space-y-6" variants={itemVariants}>
            <EnhancedFileUpload
              acceptedFileTypes={{ "application/pdf": [".pdf"] }}
              maxFiles={10}
              onFilesSelected={handleFilesSelected}
              isProcessing={isProcessing}
              processingProgress={progress}
            />

            {files.length > 1 && (
              <div className="flex justify-end">
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Button
                    onClick={handleMerge}
                    disabled={files.length < 2 || isProcessing}
                    className="px-8"
                  >
                    {isProcessing ? "Merging..." : "Merge PDFs"}
                  </Button>
                </motion.div>
              </div>
            )}
          </motion.div>

          <motion.div
            className="mt-12 bg-muted/50 p-6 rounded-lg"
            variants={itemVariants}
          >
            <h2 className="text-xl font-semibold mb-4">About PDF Merging</h2>
            <div className="space-y-4 text-sm text-muted-foreground">
              <p>
                Our PDF merge tool allows you to combine multiple PDF files into
                a single document. This is useful for creating comprehensive
                reports, combining related documents, or organizing your digital
                paperwork.
              </p>
              <p>
                The tool preserves the quality and formatting of your original
                files while creating a new, combined document that you can
                download immediately.
              </p>
              <p>
                <strong>Note:</strong> Your files are processed securely on our
                servers and are not stored permanently. They are automatically
                deleted after processing.
              </p>
            </div>
          </motion.div>
          <motion.div variants={itemVariants}>
            <SEOFaq
              title="PDF Merging FAQ"
              faqs={pdfMergeFAQs}
              toolName="PDF Merging"
            />
          </motion.div>
        </motion.div>
      </MainLayout>
    </>
  );
}
