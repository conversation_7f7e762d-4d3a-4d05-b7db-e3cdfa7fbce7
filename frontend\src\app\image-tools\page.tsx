"use client";

import { MainLayout } from "@/components/MainLayout";
import {
  Card,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { motion } from "framer-motion";
import Link from "next/link";
import { useEffect, useState } from "react";

export default function ImageToolsPage() {
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    setIsLoaded(true);
  }, []);

  const tools = [
    {
      title: "Compress Image",
      description: "Reduce the file size of your images without losing quality",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="var(--chart-1)"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="h-6 w-6"
        >
          <rect width="18" height="18" x="3" y="3" rx="2" ry="2"></rect>
          <circle cx="9" cy="9" r="2"></circle>
          <path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21"></path>
        </svg>
      ),
      href: "/compress-image",
    },
    {
      title: "Convert Image",
      description: "Convert images between different formats (PNG, JPG, WebP)",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="var(--chart-2)"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="h-6 w-6"
        >
          <path d="M21 12V7H5a2 2 0 0 1 0-4h14v4"></path>
          <path d="M3 12v5h16a2 2 0 0 1 0 4H3v-4"></path>
        </svg>
      ),
      href: "/convert-image",
    },
    {
      title: "Resize Image",
      description: "Resize your images to specific dimensions",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="var(--chart-3)"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="h-6 w-6"
        >
          <path d="M15 3h6v6"></path>
          <path d="M9 21H3v-6"></path>
          <path d="m21 3-9 9"></path>
          <path d="m3 21 9-9"></path>
        </svg>
      ),
      href: "/resize-image",
    },
    {
      title: "Crop Image",
      description: "Crop your images to specific dimensions or aspect ratios",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="var(--chart-4)"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="h-6 w-6"
        >
          <path d="M6 2v14a2 2 0 0 0 2 2h14"></path>
          <path d="M18 22V8a2 2 0 0 0-2-2H2"></path>
        </svg>
      ),
      href: "/crop-image",
    },
    {
      title: "Upscale Image",
      description: "Increase resolution and enhance image quality",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="var(--chart-5)"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="h-6 w-6"
        >
          <path d="m7 21-4.3-4.3c-1-1-1-2.5 0-3.4l9.6-9.6c1-1 2.5-1 3.4 0l5.6 5.6c1 1 1 2.5 0 3.4L13 21"></path>
          <path d="M22 21H7"></path>
          <path d="m5 11 9 9"></path>
        </svg>
      ),
      href: "/upscale-image",
    },
    {
      title: "Image to PDF",
      description: "Convert images to PDF documents with custom settings",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="var(--chart-6)"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="h-6 w-6"
        >
          <path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7l-5-5Z"></path>
          <path d="M14 2v4a2 2 0 0 0 2 2h4"></path>
          <path d="M8 10v8"></path>
          <path d="M8 10a2 2 0 1 0 0-4 2 2 0 0 0 0 4Z"></path>
          <path d="M12 18a2 2 0 1 0 0-4 2 2 0 0 0 0 4Z"></path>
          <path d="M16 14a2 2 0 1 0 0-4 2 2 0 0 0 0 4Z"></path>
        </svg>
      ),
      href: "/image-to-pdf",
    },
  ];

  const fadeInUp = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.3, ease: "easeOut" },
    },
  };

  const staggerContainer = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2,
      },
    },
  };

  return (
    <MainLayout>
      <div
        className="absolute inset-0 -z-10 opacity-20"
        style={{
          background:
            "linear-gradient(120deg, rgba(var(--primary-rgb), 0.05) 0%, rgba(var(--secondary-rgb), 0.05) 50%, rgba(var(--primary-rgb), 0.05) 100%)",
        }}
      />

      <div className="space-y-8 max-w-4xl mx-auto">
        <motion.div
          className="text-center space-y-2 pt-4"
          initial="hidden"
          animate={isLoaded ? "visible" : "hidden"}
          variants={fadeInUp}
        >
          <h1 className="text-4xl font-bold tracking-tight font-display">
            Image Tools
          </h1>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            No installation, no registration, 100% secure & free
          </p>
        </motion.div>
        <motion.div
          className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4"
          initial="hidden"
          animate={isLoaded ? "visible" : "hidden"}
          variants={staggerContainer}
        >
          {tools.map((tool) => (
            <motion.div key={tool.href} variants={fadeInUp} className="group">
              <Link href={tool.href} className="block w-full">
                <Card className="h-full transition-all duration-200 ease-out hover:scale-[1.02] hover:shadow-lg backdrop-blur-sm bg-card/95 border-primary/10 group-hover:border-primary/20">
                  <CardHeader>
                    <div className="p-1 w-10 h-10 rounded-lg bg-primary/10 flex items-center justify-center mb-3 group-hover:bg-primary/15 transition-colors duration-200">
                      {tool.icon}
                    </div>
                    <CardTitle className="font-display text-base mb-2">
                      {tool.title}
                    </CardTitle>
                    <CardDescription className="text-sm leading-relaxed">
                      {tool.description}
                    </CardDescription>
                  </CardHeader>
                </Card>
              </Link>
            </motion.div>
          ))}
        </motion.div>

        <motion.div
          className="text-center space-y-4"
          initial="hidden"
          animate={isLoaded ? "visible" : "hidden"}
          variants={{
            hidden: { opacity: 0 },
            visible: {
              opacity: 1,
              transition: { delay: 0.6, duration: 0.5 },
            },
          }}
        >
          <h2 className="text-xl font-bold font-display">How It Works</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-3xl mx-auto">
            {[
              {
                step: 1,
                title: "Upload Files",
                description:
                  "Drag and drop your images or click to select them from your device",
              },
              {
                step: 2,
                title: "Process",
                description:
                  "Our system will process your images quickly and efficiently",
              },
              {
                step: 3,
                title: "Download",
                description:
                  "Download your processed images immediately to your device",
              },
            ].map((step) => (
              <div
                key={step.step}
                className="flex flex-col items-center space-y-3 group cursor-default"
              >
                <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center font-bold text-primary group-hover:bg-primary/15 transition-colors duration-200">
                  {step.step}
                </div>
                <h3 className="text-sm font-semibold font-display">
                  {step.title}
                </h3>
                <p className="text-xs text-muted-foreground text-center leading-relaxed">
                  {step.description}
                </p>
              </div>
            ))}
          </div>
        </motion.div>
      </div>
    </MainLayout>
  );
}
