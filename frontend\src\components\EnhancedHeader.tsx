import { Button } from "@/components/ui/button";
import Image from "next/image";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { memo, useCallback, useState } from "react";

const NavItem = memo(
  ({
    href,
    label,
    isActive,
  }: {
    href: string;
    label: string;
    isActive: boolean;
  }) => (
    <Link href={href}>
      <span
        className={`text-sm font-medium transition-colors duration-150 ${
          isActive ? "text-primary" : "text-muted-foreground hover:text-primary"
        }`}
      >
        {label}
        {isActive && <div className="h-0.5 bg-primary mt-1 rounded-full" />}
      </span>
    </Link>
  )
);

NavItem.displayName = "NavItem";

const MobileNavItem = memo(
  ({
    href,
    label,
    isActive,
    onClick,
  }: {
    href: string;
    label: string;
    isActive: boolean;
    onClick: () => void;
  }) => (
    <Link
      href={href}
      className={`block py-3 text-sm font-medium transition-colors duration-150 ${
        isActive ? "text-primary" : "text-muted-foreground hover:text-primary"
      }`}
      onClick={onClick}
    >
      {label}
    </Link>
  )
);

MobileNavItem.displayName = "MobileNavItem";

export function EnhancedHeader() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const pathname = usePathname();

  const navItems = [
    { href: "/pdf-tools", label: "PDF Tools" },
    { href: "/image-tools", label: "Image Tools" },
  ];

  const toggleMenu = useCallback(() => {
    setIsMenuOpen((prev) => !prev);
  }, []);

  const closeMenu = useCallback(() => {
    setIsMenuOpen(false);
  }, []);

  return (
    <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container mx-auto px-4 h-16 flex items-center justify-between">
        <Link href="/" className="flex items-center group">
          <div className="transition-transform duration-200 group-hover:scale-110">
            <Image
              src="/logo.svg"
              alt="FreeFox Logo"
              width={48}
              height={48}
              className="h-12 w-12"
              priority
            />
          </div>
          <span className="font-bold text-xl">FreeFox</span>
        </Link>

        <nav className="hidden md:flex items-center space-x-8">
          {navItems.map((item) => (
            <NavItem
              key={item.href}
              href={item.href}
              label={item.label}
              isActive={pathname === item.href}
            />
          ))}
        </nav>

        <div className="md:hidden">
          <Button
            variant="ghost"
            size="icon"
            onClick={toggleMenu}
            className="h-10 w-10"
            aria-label="Toggle menu"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className={`h-5 w-5 transition-transform duration-200 ${
                isMenuOpen ? "rotate-90" : ""
              }`}
            >
              <line x1="4" x2="20" y1="12" y2="12" />
              <line x1="4" x2="20" y1="6" y2="6" />
              <line x1="4" x2="20" y1="18" y2="18" />
            </svg>
          </Button>
        </div>
      </div>

      {isMenuOpen && (
        <div className="md:hidden border-t bg-background/95 backdrop-blur">
          <div className="px-4 py-2 space-y-1">
            {navItems.map((item) => (
              <MobileNavItem
                key={item.href}
                href={item.href}
                label={item.label}
                isActive={pathname === item.href}
                onClick={closeMenu}
              />
            ))}
          </div>
        </div>
      )}
    </header>
  );
}
