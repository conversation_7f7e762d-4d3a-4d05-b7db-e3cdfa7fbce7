"use client";

import { MainLayout } from "@/components/MainLayout";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import Link from "next/link";
import { useEffect } from "react";

const jsonLd = {
  "@context": "https://schema.org",
  "@type": "WebApplication",
  name: "FreeFox",
  description:
    "Free online PDF and image tools for compression, conversion, merging, and more",
  url: "https://freefox.com",
  applicationCategory: "UtilitiesApplication",
  operatingSystem: "Any",
  offers: {
    "@type": "Offer",
    price: "0",
    priceCurrency: "USD",
  },
  featureList: [
    "PDF Compression",
    "PDF Merging",
    "PDF Splitting",
    "Image Compression",
    "Image Conversion",
    "Image Resizing",
  ],
};

export default function Home() {
  // Set page metadata on client side
  useEffect(() => {
    document.title =
      "Free PDF & Image Tools Online - Compress, Convert, Merge PDFs | FreeFox";

    // Update meta description
    const metaDescription = document.querySelector('meta[name="description"]');
    if (metaDescription) {
      metaDescription.setAttribute(
        "content",
        "Free online PDF and image tools. Compress PDFs, merge PDFs, convert images, resize photos, and more. No registration required. 100% secure and fast processing."
      );
    }

    // Update canonical URL
    let canonical = document.querySelector('link[rel="canonical"]');
    if (!canonical) {
      canonical = document.createElement("link");
      canonical.setAttribute("rel", "canonical");
      document.head.appendChild(canonical);
    }
    canonical.setAttribute("href", "https://freefox.com");
  }, []);

  const pdfTools = [
    {
      title: "Merge PDF",
      description: "Combine multiple PDF files into one document",
      href: "/merge-pdf",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="var(--chart-1)"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="h-8 w-8"
        >
          <path d="M8 3H5a2 2 0 0 0-2 2v14c0 1.1.9 2 2 2h14a2 2 0 0 0 2-2V8h-3"></path>
          <path d="M17 21v-8h8"></path>
          <path d="M17 13 7 3"></path>
        </svg>
      ),
    },
    {
      title: "Split PDF",
      description: "Extract pages or split PDF into multiple files",
      href: "/split-pdf",
      icon: "✂️",
    },
    {
      title: "Compress PDF",
      description: "Reduce PDF file size while maintaining quality",
      href: "/compress-pdf",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="var(--chart-2)"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="h-8 w-8"
        >
          <path d="M5 8V5c0-1 1-2 2-2h10c1 0 2 1 2 2v3"></path>
          <path d="M19 16v3c0 1-1 2-2 2H7c-1 0-2-1-2-2v-3"></path>
          <line x1="12" x2="12" y1="4" y2="20"></line>
          <polyline points="9 7 12 4 15 7"></polyline>
          <polyline points="15 17 12 20 9 17"></polyline>
        </svg>
      ),
    },
  ];

  const imageTools = [
    {
      title: "Compress Image",
      description: "Reduce image file size without losing quality",
      href: "/compress-image",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="var(--chart-1)"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="h-8 w-8"
        >
          <rect width="18" height="18" x="3" y="3" rx="2" ry="2"></rect>
          <circle cx="9" cy="9" r="2"></circle>
          <path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21"></path>
        </svg>
      ),
    },
    {
      title: "Convert Image",
      description: "Convert between PNG, JPG, WebP formats",
      href: "/convert-image",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="var(--chart-2)"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="h-8 w-8"
        >
          <path d="M21 12V7H5a2 2 0 0 1 0-4h14v4"></path>
          <path d="M3 12v5h16a2 2 0 0 1 0 4H3v-4"></path>
        </svg>
      ),
    },
    {
      title: "Resize Image",
      description: "Change image dimensions and resolution",
      href: "/resize-image",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="var(--chart-3)"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="h-8 w-8"
        >
          <path d="M15 3h6v6"></path>
          <path d="M9 21H3v-6"></path>
          <path d="m21 3-9 9"></path>
          <path d="m3 21 9-9"></path>
        </svg>
      ),
    },
  ];

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />
      <MainLayout>
        <div className="max-w-6xl mx-auto">
          {/* Hero Section */}
          <section className="text-center py-8 space-y-6">
            <h1 className="text-5xl font-bold tracking-tight font-display">
              Free PDF & Image Tools Online
            </h1>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              Professional-grade PDF and image processing tools. Compress,
              convert, merge, and edit your files instantly. No registration
              required, 100% secure, and completely free.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild size="lg" className="px-8">
                <Link href="/pdf-tools">Explore PDF Tools</Link>
              </Button>
              <Button asChild variant="outline" size="lg" className="px-8">
                <Link href="/image-tools">Explore Image Tools</Link>
              </Button>
            </div>
          </section>

          {/* PDF Tools Section */}
          <section className="py-8">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold font-display mb-4">
                PDF Tools
              </h2>
              <p className="text-muted-foreground max-w-2xl mx-auto">
                Powerful PDF utilities to handle all your document processing
                needs
              </p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
              {pdfTools.map((tool) => (
                <Card
                  key={tool.href}
                  className="hover:shadow-lg transition-shadow"
                >
                  <CardHeader>
                    <div className="text-3xl mb-2">{tool.icon}</div>
                    <CardTitle className="font-display">{tool.title}</CardTitle>
                    <CardDescription>{tool.description}</CardDescription>
                    <Button asChild className="mt-4">
                      <Link href={tool.href}>Use Tool</Link>
                    </Button>
                  </CardHeader>
                </Card>
              ))}
            </div>
            <div className="text-center">
              <Button asChild variant="outline">
                <Link href="/pdf-tools">View All PDF Tools</Link>
              </Button>
            </div>
          </section>

          {/* Image Tools Section */}
          <section className="py-8">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold font-display mb-4">
                Image Tools
              </h2>
              <p className="text-muted-foreground max-w-2xl mx-auto">
                Advanced image processing tools for all your photo editing needs
              </p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
              {imageTools.map((tool) => (
                <Card
                  key={tool.href}
                  className="hover:shadow-lg transition-shadow"
                >
                  <CardHeader>
                    <div className="text-3xl mb-2">{tool.icon}</div>
                    <CardTitle className="font-display">{tool.title}</CardTitle>
                    <CardDescription>{tool.description}</CardDescription>
                    <Button asChild className="mt-4">
                      <Link href={tool.href}>Use Tool</Link>
                    </Button>
                  </CardHeader>
                </Card>
              ))}
            </div>
            <div className="text-center">
              <Button asChild variant="outline">
                <Link href="/image-tools">View All Image Tools</Link>
              </Button>
            </div>
          </section>

          {/* Features Section */}
          <section className="py-8 bg-muted/20 rounded-lg">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold font-display mb-4">
                Why Choose FreeFox?
              </h2>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="text-center">
                <div className="text-4xl mb-4">🔒</div>
                <h3 className="text-xl font-semibold mb-2">100% Secure</h3>
                <p className="text-muted-foreground">
                  Your files are processed securely and deleted immediately
                  after processing
                </p>
              </div>
              <div className="text-center">
                <div className="text-4xl mb-4">⚡</div>
                <h3 className="text-xl font-semibold mb-2">Lightning Fast</h3>
                <p className="text-muted-foreground">
                  Advanced algorithms ensure quick processing without
                  compromising quality
                </p>
              </div>
              <div className="text-center">
                <div className="text-4xl mb-4">🆓</div>
                <h3 className="text-xl font-semibold mb-2">Completely Free</h3>
                <p className="text-muted-foreground">
                  No registration, no hidden fees, no watermarks - just free
                  tools
                </p>
              </div>
            </div>
          </section>
        </div>
      </MainLayout>
    </>
  );
}
