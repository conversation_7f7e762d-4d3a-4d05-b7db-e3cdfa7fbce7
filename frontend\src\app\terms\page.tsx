"use client";

import { MainLayout } from "@/components/MainLayout";
import { motion } from "framer-motion";
import Link from "next/link";

export default function TermsPage() {
  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { type: "spring", stiffness: 500, damping: 30 },
    },
  };

  return (
    <MainLayout>
      <motion.div
        className="max-w-3xl mx-auto py-8"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        <motion.div className="mb-8" variants={itemVariants}>
          <h1 className="text-3xl font-bold font-display mb-2">
            Terms & Conditions
          </h1>
          <p className="text-muted-foreground">
            Last updated:{" "}
            {new Date().toLocaleDateString("en-US", {
              month: "long",
              day: "numeric",
              year: "numeric",
            })}
          </p>
        </motion.div>

        <motion.div
          className="prose prose-stone dark:prose-invert max-w-none space-y-6"
          variants={itemVariants}
        >
          <section className="space-y-4">
            <h2 className="text-xl font-semibold font-display">
              1. Introduction
            </h2>
            <p className="text-muted-foreground">
              Welcome to FreeFox. These Terms & Conditions govern your use of
              our website and services. By accessing or using our services, you
              agree to be bound by these Terms. If you disagree with any part of
              these terms, you may not access our services.
            </p>
          </section>

          <section className="space-y-4">
            <h2 className="text-xl font-semibold font-display">
              2. Use of Services
            </h2>
            <p className="text-muted-foreground">
              Our services are provided &quot;as is&quot; and &quot;as
              available&quot; for your use, without warranties of any kind,
              either express or implied. We do not guarantee that our services
              will be uninterrupted, timely, secure, or error-free.
            </p>
            <p className="text-muted-foreground">
              You are responsible for maintaining the confidentiality of your
              account and for all activities that occur under your account. You
              agree to notify us immediately of any unauthorized use of your
              account or any other breach of security.
            </p>
          </section>

          <section className="space-y-4">
            <h2 className="text-xl font-semibold font-display">
              3. File Processing
            </h2>
            <p className="text-muted-foreground">
              Our services allow you to process various file types including
              PDFs and images. We do not store your files permanently. All files
              are processed on-the-fly and are automatically deleted once
              processing is complete.
            </p>
            <p className="text-muted-foreground">
              You retain all ownership rights to your content. By uploading
              content to our service, you grant us a limited license to use,
              process, and store your content solely for the purpose of
              providing our services to you.
            </p>
          </section>

          <section className="space-y-4">
            <h2 className="text-xl font-semibold font-display">
              4. Limitations
            </h2>
            <p className="text-muted-foreground">
              You agree not to use our services:
            </p>
            <ul className="list-disc pl-6 text-muted-foreground space-y-2">
              <li>
                In any way that violates any applicable laws or regulations
              </li>
              <li>
                To transmit any material that contains viruses, trojan horses,
                or other harmful code
              </li>
              <li>
                To infringe upon or violate our intellectual property rights or
                those of others
              </li>
              <li>To harass, abuse, or harm another person</li>
              <li>To upload invalid data, viruses, or other malicious code</li>
            </ul>
          </section>

          <section className="space-y-4">
            <h2 className="text-xl font-semibold font-display">
              5. Changes to Terms
            </h2>
            <p className="text-muted-foreground">
              We reserve the right to modify these terms at any time. We will
              provide notice of significant changes by updating the date at the
              top of these terms and by maintaining a current version of the
              terms on this page.
            </p>
            <p className="text-muted-foreground">
              Your continued use of our services after such modifications will
              constitute your acknowledgment of the modified terms and agreement
              to abide by them.
            </p>
          </section>

          <section className="space-y-4">
            <h2 className="text-xl font-semibold font-display">
              6. Contact Us
            </h2>
            <p className="text-muted-foreground">
              If you have any questions about these Terms & Conditions, please
              contact us at{" "}
              <a
                href={`mailto:${
                  process.env.NEXT_PUBLIC_CONTACT_EMAIL || "<EMAIL>"
                }`}
                className="text-primary hover:underline"
              >
                {process.env.NEXT_PUBLIC_CONTACT_EMAIL || "<EMAIL>"}
              </a>
              .
            </p>
          </section>
        </motion.div>

        <motion.div className="mt-12 pt-6 border-t" variants={itemVariants}>
          <Link
            href="/"
            className="text-primary hover:underline flex items-center gap-2"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="h-4 w-4"
            >
              <path d="m12 19-7-7 7-7" />
              <path d="M19 12H5" />
            </svg>
            Back to Home
          </Link>
        </motion.div>
      </motion.div>
    </MainLayout>
  );
}
