import asyncio
import io
import os
import tempfile
import zipfile
from concurrent.futures import ThreadPoolExecutor
from pathlib import Path
from typing import List, Optional

import fitz  # PyMuPDF
from app.middleware import add_context_data, log_error_with_context
from app.utils.docx_converter import convert_docx_to_pdf
from app.utils.error_logger import Operation, log_info, log_warning
from app.utils.pdf_compress_image import _compress_page_images
from fastapi import APIRouter, File, Form, HTTPException, Query, UploadFile
from fastapi.responses import StreamingResponse

router = APIRouter()

# Thread pool for CPU-intensive operations
executor = ThreadPoolExecutor(max_workers=4)

async def save_upload_file_optimized(upload_file: UploadFile, destination: str) -> int:
    """Optimized file saving with larger chunks and async I/O"""
    file_size = 0
    chunk_size = 8 * 1024 * 1024  # 8MB chunks for better I/O performance

    with open(destination, "wb") as f:
        while True:
            chunk = await upload_file.read(chunk_size)
            if not chunk:
                break
            f.write(chunk)
            file_size += len(chunk)

    return file_size

def process_pdf_merge(file_paths: List[str], output_path: str) -> int:
    """CPU-intensive merge operation in thread pool"""
    merged_pdf = fitz.open()
    page_count = 0

    try:
        for file_path in file_paths:
            try:
                input_pdf = fitz.open(file_path)
                merged_pdf.insert_pdf(input_pdf)
                page_count += input_pdf.page_count
                input_pdf.close()
            except Exception as e:
                log_warning(f"Skipping corrupted file {file_path}: {e}", operation=Operation.FILE_CORRUPTED)
                continue

        if page_count == 0:
            raise ValueError("No valid pages to merge")

        # Optimized save with maximum compression
        merged_pdf.save(output_path, garbage=4, deflate=True, clean=True, pretty=False)
        return page_count
    finally:
        merged_pdf.close()

@router.post("/merge-pdf")
async def merge_pdf(files: list[UploadFile] = File(...)):
    """Merge multiple PDF files with optimized performance"""
    if not files:
        raise HTTPException(status_code=400, detail="No files provided")

    try:
        with tempfile.TemporaryDirectory() as temp_dir:
            # Save files concurrently
            save_tasks = []
            file_paths = []

            for i, file in enumerate(files):
                if not file.filename.lower().endswith('.pdf'):
                    continue

                temp_file_path = os.path.join(temp_dir, f"input_{i}.pdf")
                file_paths.append(temp_file_path)
                save_tasks.append(save_upload_file_optimized(file, temp_file_path))

            if not file_paths:
                raise HTTPException(status_code=400, detail="No valid PDF files provided")

            # Wait for all files to be saved
            await asyncio.gather(*save_tasks)

            # Perform merge in thread pool
            output_path = os.path.join(temp_dir, "merged.pdf")
            loop = asyncio.get_event_loop()
            page_count = await loop.run_in_executor(executor, process_pdf_merge, file_paths, output_path)

            with open(output_path, "rb") as f:
                file_content = f.read()

            return StreamingResponse(
                io.BytesIO(file_content),
                media_type="application/pdf",
                headers={"Content-Disposition": "attachment; filename=merged.pdf"}
            )
    except Exception as e:
        add_context_data('operation', 'pdf_merge')
        add_context_data('file_count', len(files))
        log_error_with_context(
            f"PDF merge operation failed: {str(e)}",
            exc=e,
            extra_data={'file_count': len(files), 'operation': 'pdf_merge'}
        )
        raise HTTPException(status_code=500, detail=f"Error merging PDFs: {str(e)}")

def process_pdf_compression(input_path: str, output_path: str, quality: int,
                          image_dpi: int, remove_annotations: bool,
                          compress_images: bool, remove_metadata: bool) -> tuple:
    """CPU-intensive compression in thread pool"""
    doc = fitz.open(input_path)
    original_size = os.path.getsize(input_path)

    try:
        if remove_metadata:
            doc.set_metadata({})

        # Process pages in batches for memory efficiency
        batch_size = 10
        total_pages = doc.page_count

        for batch_start in range(0, total_pages, batch_size):
            batch_end = min(batch_start + batch_size, total_pages)

            for page_num in range(batch_start, batch_end):
                page = doc.load_page(page_num)

                if compress_images:
                    _compress_page_images(page, quality, image_dpi)

                if remove_annotations:
                    # More efficient annotation removal
                    annots = list(page.annots())
                    for annot in reversed(annots):
                        page.delete_annot(annot)

        # Maximum compression settings
        doc.save(output_path, garbage=4, deflate=True, clean=True, pretty=False,
                linear=True, no_new_id=True)

        compressed_size = os.path.getsize(output_path)
        compression_ratio = (1 - (compressed_size / original_size)) * 100 if original_size > 0 else 0

        return original_size, compressed_size, compression_ratio
    finally:
        doc.close()

@router.post("/compress-pdf")
async def compress_pdf(
    file: UploadFile = File(...),
    quality: int = Query(30, ge=10, le=100),
    image_dpi: int = Query(72, ge=72, le=300),
    remove_annotations: bool = Query(True),
    compress_images: bool = Query(True),
    remove_metadata: bool = Query(True),
):
    """High-performance PDF compression"""
    if not file.filename.lower().endswith('.pdf'):
        raise HTTPException(status_code=400, detail="File must be a PDF")

    try:
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_input_path = os.path.join(temp_dir, "input.pdf")
            temp_output_path = os.path.join(temp_dir, "compressed.pdf")

            # Save file optimized
            await save_upload_file_optimized(file, temp_input_path)

            # Compress in thread pool
            loop = asyncio.get_event_loop()
            original_size, compressed_size, compression_ratio = await loop.run_in_executor(
                executor, process_pdf_compression, temp_input_path, temp_output_path,
                quality, image_dpi, remove_annotations, compress_images, remove_metadata
            )

            log_info(
                f"PDF Compression successful: {file.filename} - "
                f"Original: {original_size/1024:.2f}KB, "
                f"Compressed: {compressed_size/1024:.2f}KB, "
                f"Reduction: {compression_ratio:.2f}%",
                operation=Operation.PDF_COMPRESS
            )

            with open(temp_output_path, "rb") as f:
                file_content = f.read()

            response_headers = {
                "Content-Disposition": f"attachment; filename=compressed_{file.filename}",
                "X-Compression-Ratio": f"{compression_ratio:.2f}%",
                "X-Original-Size": str(original_size),
                "X-Compressed-Size": str(compressed_size),
                "Access-Control-Expose-Headers": (
                    "Content-Disposition, "
                    "X-Compression-Ratio, "
                    "X-Original-Size, "
                    "X-Compressed-Size"
                )
            }

            return StreamingResponse(
                io.BytesIO(file_content),
                media_type="application/pdf",
                headers=response_headers
            )
    except Exception as e:
        add_context_data('operation', 'pdf_compression')
        add_context_data('filename', file.filename)
        add_context_data('compression_settings', {
            'quality': quality,
            'image_dpi': image_dpi,
            'remove_annotations': remove_annotations,
            'compress_images': compress_images,
            'remove_metadata': remove_metadata
        })
        log_error_with_context(
            f"PDF compression failed for {file.filename}: {str(e)}",
            exc=e,
            extra_data={'operation': 'pdf_compress'}
        )
        raise HTTPException(status_code=500, detail=f"Error compressing PDF: {str(e)}")

@router.post("/word-to-pdf")
async def word_to_pdf(file: UploadFile = File(...)):
    """Convert Word to PDF with optimized I/O"""
    if not file.filename.lower().endswith(('.doc', '.docx')):
        raise HTTPException(status_code=400, detail="File must be a Word document (.doc or .docx)")

    try:
        with tempfile.TemporaryDirectory() as temp_dir:
            file_extension = Path(file.filename).suffix
            temp_input_path = os.path.join(temp_dir, f"input{file_extension}")
            temp_output_path = os.path.join(temp_dir, "output.pdf")

            # Optimized file saving
            await save_upload_file_optimized(file, temp_input_path)

            # Convert in thread pool to avoid blocking
            loop = asyncio.get_event_loop()
            success = await loop.run_in_executor(
                executor, convert_docx_to_pdf, temp_input_path, temp_output_path
            )

            if not success or not os.path.exists(temp_output_path) or os.path.getsize(temp_output_path) == 0:
                raise HTTPException(status_code=500, detail="Failed to convert Word document to PDF")

            with open(temp_output_path, "rb") as f:
                file_content = f.read()

            output_filename = f"{os.path.splitext(file.filename)[0]}.pdf"
            return StreamingResponse(
                io.BytesIO(file_content),
                media_type="application/pdf",
                headers={"Content-Disposition": f"attachment; filename={output_filename}"}
            )
    except Exception as e:
        logger.error(f"Error processing Word document: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error processing Word document: {str(e)}")

def process_pdf_split(input_path: str, temp_dir: str, split_type: str,
                     page_ranges: Optional[str], total_pages: int) -> dict:
    """CPU-intensive split operation in thread pool"""
    source_doc = fitz.open(input_path)
    pages_to_extract_map = {}

    try:
        if split_type == 'all':
            # Pre-allocate for better performance
            pages_to_extract_map = {f"page_{i+1}.pdf": [i] for i in range(total_pages)}
        elif split_type == 'range' and page_ranges:
            for r_str in page_ranges.split(','):
                r_str = r_str.strip()
                if '-' in r_str:
                    start, end = map(int, r_str.split('-'))
                    start = max(1, min(start, total_pages))
                    end = min(total_pages, max(end, start))
                    if start <= end:
                        output_filename = f"range_{start}-{end}.pdf"
                        pages_to_extract_map[output_filename] = list(range(start - 1, end))
                else:
                    page = int(r_str)
                    if 1 <= page <= total_pages:
                        pages_to_extract_map[f"page_{page}.pdf"] = [page - 1]

        if not pages_to_extract_map:
            raise ValueError("No pages selected for extraction")

        # Create PDFs efficiently
        output_files = {}
        for output_filename, page_indices in pages_to_extract_map.items():
            output_path = os.path.join(temp_dir, output_filename)
            new_pdf = fitz.open()

            try:
                # Use efficient page insertion
                for page_idx in page_indices:
                    new_pdf.insert_pdf(source_doc, from_page=page_idx, to_page=page_idx)

                new_pdf.save(output_path, garbage=4, deflate=True, clean=True, pretty=False)
                output_files[output_filename] = output_path
            finally:
                new_pdf.close()

        return output_files
    finally:
        source_doc.close()

@router.post("/split-pdf")
async def split_pdf(
    file: UploadFile = File(...),
    split_type: str = Form(...),
    page_ranges: Optional[str] = Form(None)
):
    """High-performance PDF splitting"""
    if not file.filename.lower().endswith('.pdf'):
        raise HTTPException(status_code=400, detail="File must be a PDF")

    try:
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_input_path = os.path.join(temp_dir, "input.pdf")

            await save_upload_file_optimized(file, temp_input_path)

            # Quick page count check
            with fitz.open(temp_input_path) as doc:
                total_pages = doc.page_count

            # Process split in thread pool
            loop = asyncio.get_event_loop()
            output_files = await loop.run_in_executor(
                executor, process_pdf_split, temp_input_path, temp_dir,
                split_type, page_ranges, total_pages
            )

            if len(output_files) == 1:
                output_filename, output_path = list(output_files.items())[0]
                with open(output_path, "rb") as f:
                    file_content = f.read()

                return StreamingResponse(
                    io.BytesIO(file_content),
                    media_type="application/pdf",
                    headers={"Content-Disposition": f"attachment; filename={output_filename}"}
                )
            else:
                # Create zip efficiently
                zip_filename = f"split_{os.path.splitext(file.filename)[0]}.zip"
                zip_path = os.path.join(temp_dir, zip_filename)

                with zipfile.ZipFile(zip_path, 'w', compression=zipfile.ZIP_DEFLATED,
                                   compresslevel=6) as zip_file:
                    for output_filename, output_path in output_files.items():
                        zip_file.write(output_path, arcname=output_filename)

                with open(zip_path, "rb") as f:
                    file_content = f.read()

                return StreamingResponse(
                    io.BytesIO(file_content),
                    media_type="application/zip",
                    headers={"Content-Disposition": f"attachment; filename={zip_filename}"}
                )
    except Exception as e:
        logger.error(f"Error splitting PDF: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error splitting PDF: {str(e)}")

def process_pdf_rotation(file_paths: List[tuple], rotation_angle: int, temp_dir: str) -> List[tuple]:
    """CPU-intensive rotation in thread pool"""
    processed_files = []

    for temp_input_path, original_filename in file_paths:
        doc = fitz.open(temp_input_path)

        try:
            # Batch rotate pages for efficiency
            for page in doc:
                current_rotation = page.rotation
                new_rotation = (current_rotation + rotation_angle) % 360
                page.set_rotation(new_rotation)

            output_filename = f"rotated_{original_filename}"
            temp_output_path = os.path.join(temp_dir, output_filename)
            doc.save(temp_output_path, garbage=4, deflate=True, clean=True, pretty=False)
            processed_files.append((temp_output_path, output_filename))
        finally:
            doc.close()

    return processed_files

@router.post("/rotate-pdf")
async def rotate_pdf(
    files: List[UploadFile] = File(...),
    rotation_angle: int = Form(...)
):
    """High-performance PDF rotation"""
    if not files:
        raise HTTPException(status_code=400, detail="No files provided")

    if rotation_angle not in [0, 90, 180, 270]:
        raise HTTPException(status_code=400, detail="Rotation angle must be 0, 90, 180, or 270 degrees")

    try:
        with tempfile.TemporaryDirectory() as temp_dir:
            # Save files concurrently
            save_tasks = []
            file_paths = []

            for file_idx, file in enumerate(files):
                if not file.filename.lower().endswith('.pdf'):
                    continue

                temp_input_path = os.path.join(temp_dir, f"input_{file_idx}_{file.filename}")
                file_paths.append((temp_input_path, file.filename))
                save_tasks.append(save_upload_file_optimized(file, temp_input_path))

            if not file_paths:
                raise HTTPException(status_code=400, detail="No PDF files provided")

            await asyncio.gather(*save_tasks)

            # Rotate in thread pool
            loop = asyncio.get_event_loop()
            processed_files = await loop.run_in_executor(
                executor, process_pdf_rotation, file_paths, rotation_angle, temp_dir
            )

            if len(processed_files) == 1:
                temp_output_path, output_filename = processed_files[0]
                with open(temp_output_path, "rb") as f:
                    file_content = f.read()

                return StreamingResponse(
                    io.BytesIO(file_content),
                    media_type="application/pdf",
                    headers={"Content-Disposition": f"attachment; filename={output_filename}"}
                )
            else:
                zip_path = os.path.join(temp_dir, "rotated_pdfs.zip")
                with zipfile.ZipFile(zip_path, 'w', compression=zipfile.ZIP_DEFLATED,
                                   compresslevel=6) as zip_file:
                    for temp_output_path, output_filename in processed_files:
                        zip_file.write(temp_output_path, arcname=output_filename)

                with open(zip_path, "rb") as f:
                    file_content = f.read()

                return StreamingResponse(
                    io.BytesIO(file_content),
                    media_type="application/zip",
                    headers={"Content-Disposition": "attachment; filename=rotated_pdfs.zip"}
                )

    except Exception as e:
        logger.error(f"Error rotating PDF(s): {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error rotating PDF(s): {str(e)}")

def process_pdf_to_word(input_path: str, output_path: str) -> bool:
    """CPU-intensive PDF to Word conversion in thread pool"""
    try:
        from pdf2docx import Converter

        cv = Converter(input_path)
        cv.convert(output_path)
        cv.close()

        return os.path.exists(output_path) and os.path.getsize(output_path) > 0
    except ImportError:
        raise ImportError("pdf2docx library is not installed")
    except Exception as e:
        logger.error(f"PDF to Word conversion failed: {str(e)}")
        return False

@router.post("/pdf-to-word")
async def pdf_to_word(file: UploadFile = File(...)):
    """High-performance PDF to Word conversion"""
    if not file.filename.lower().endswith('.pdf'):
        raise HTTPException(status_code=400, detail="File must be a PDF")

    try:
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_input_path = os.path.join(temp_dir, "input.pdf")
            output_filename = f"{os.path.splitext(file.filename)[0]}.docx"
            temp_output_path = os.path.join(temp_dir, output_filename)

            await save_upload_file_optimized(file, temp_input_path)

            # Convert in thread pool
            loop = asyncio.get_event_loop()
            success = await loop.run_in_executor(
                executor, process_pdf_to_word, temp_input_path, temp_output_path
            )

            if not success:
                raise HTTPException(status_code=500, detail="Failed to convert PDF to Word")

            with open(temp_output_path, "rb") as f:
                file_content = f.read()

            return StreamingResponse(
                io.BytesIO(file_content),
                media_type="application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                headers={"Content-Disposition": f"attachment; filename={output_filename}"}
            )
    except ImportError:
        raise HTTPException(status_code=501, detail="PDF to Word conversion tool (pdf2docx) is not available")
    except Exception as e:
        logger.error(f"Error processing PDF to Word request: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error processing PDF for Word conversion: {str(e)}")