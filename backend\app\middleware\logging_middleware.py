"""
Logging Middleware for Request Context Tracking

This middleware captures request information and makes it available
for error logging throughout the request lifecycle.
"""

import time
import uuid
from contextvars import ContextVar
from typing import Any, Dict, Optional

from app.utils.error_logger import log_error, log_info
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware

# Context variables for request tracking
request_context: ContextVar[Dict[str, Any]] = ContextVar('request_context', default={})


class LoggingMiddleware(BaseHTTPMiddleware):
    """Middleware to track request context and log request/response information"""

    def __init__(self, app, log_requests: bool = True):
        super().__init__(app)
        self.log_requests = log_requests

    async def dispatch(self, request: Request, call_next):
        # Generate unique request ID
        request_id = str(uuid.uuid4())[:8]
        start_time = time.time()

        # Extract request information
        context = {
            'request_id': request_id,
            'method': request.method,
            'endpoint': str(request.url.path),
            'query_params': str(request.query_params) if request.query_params else None,
            'user_agent': request.headers.get('user-agent', 'Unknown'),
            'client_ip': self._get_client_ip(request),
            'content_type': request.headers.get('content-type'),
            'content_length': request.headers.get('content-length'),
        }

        # Set context for this request
        request_context.set(context)

        # Log incoming request (info level - console only)
        if self.log_requests:
            log_info(
                f"{context['method']} {context['endpoint']}",
                operation="request_received",
                **context
            )

        try:
            # Process the request
            response = await call_next(request)

            # Calculate processing time
            process_time = time.time() - start_time

            # Add processing time to response headers
            response.headers["X-Process-Time"] = str(process_time)
            response.headers["X-Request-ID"] = request_id

            # Log successful response
            if self.log_requests:
                operation = "response_sent" if response.status_code < 400 else "failure"
                log_info(
                    f"{context['method']} {context['endpoint']} → {response.status_code} ({process_time:.3f}s)",
                    operation=operation,
                    status_code=response.status_code,
                    process_time=process_time,
                    **context
                )

            return response

        except Exception as exc:
            # Calculate processing time for failed requests
            process_time = time.time() - start_time

            # Log the error with full context
            log_error(
                f"Request failed: {context['method']} {context['endpoint']}",
                exc=exc,
                request_context=context,
                extra_data={
                    'process_time': process_time,
                    'error_type': type(exc).__name__,
                },
                operation="failure"
            )

            # Re-raise the exception to be handled by FastAPI's exception handlers
            raise exc

    def _get_client_ip(self, request: Request) -> str:
        """Extract client IP address from request headers"""
        # Check for forwarded headers (common in reverse proxy setups)
        forwarded_for = request.headers.get('x-forwarded-for')
        if forwarded_for:
            return forwarded_for.split(',')[0].strip()

        real_ip = request.headers.get('x-real-ip')
        if real_ip:
            return real_ip

        # Fallback to direct client IP
        if hasattr(request, 'client') and request.client:
            return request.client.host

        return 'Unknown'


def get_request_context() -> Dict[str, Any]:
    """Get the current request context"""
    return request_context.get({})


def add_context_data(key: str, value: Any):
    """Add additional data to the current request context"""
    context = request_context.get({})
    context[key] = value
    request_context.set(context)


def log_error_with_context(message: str, exc: Optional[Exception] = None,
                          extra_data: Optional[Dict[str, Any]] = None):
    """
    Log an error with the current request context

    Args:
        message: Error message
        exc: Exception object (optional)
        extra_data: Additional data to include in the log
    """
    context = get_request_context()
    log_error(
        message=message,
        exc=exc,
        request_context=context,
        extra_data=extra_data
    )
