import { Toaster } from "@/components/ui/sonner";
import type { <PERSON>ada<PERSON>, Viewport } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_<PERSON>o, <PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "next/font/google";
import "./fonts.css";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
  display: "swap",
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
  display: "swap",
});

const poppins = Poppins({
  variable: "--font-poppins",
  subsets: ["latin"],
  weight: ["400", "500", "600", "700"],
  display: "swap",
});

const sora = Sora({
  variable: "--font-sora",
  subsets: ["latin"],
  weight: ["400", "500", "600", "700"],
  display: "swap",
});

export const metadata: Metadata = {
  metadataBase: new URL("https://freefox.com"),
  title: {
    default:
      "FreeFox - Free PDF & Image Tools Online | Compress, Convert, Merge PDFs",
    template: "%s | FreeFox - Free PDF & Image Tools",
  },
  description:
    "Free online PDF and image tools. Compress PDFs, merge PDFs, convert images, resize photos, and more. No registration required. 100% secure and fast processing.",
  keywords: [
    "PDF tools",
    "image tools",
    "compress PDF",
    "merge PDF",
    "split PDF",
    "PDF converter",
    "image compressor",
    "image converter",
    "resize image",
    "free PDF tools",
    "online PDF editor",
    "PDF utilities",
    "image utilities",
    "convert PDF to Word",
    "Word to PDF",
    "image to PDF",
    "crop image",
    "upscale image",
    "rotate PDF",
    "free online tools",
  ],
  icons: {
    icon: [
      { url: "/favicon.ico", sizes: "any" },
      { url: "/favicon.ico", type: "image/x-icon" },
    ],
    apple: [{ url: "/apple-icon.png", sizes: "180x180", type: "image/png" }],
  },
  authors: [{ name: "FreeFox Team" }],
  creator: "FreeFox",
  publisher: "FreeFox",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://freefox.com",
    siteName: "FreeFox",
    title: "FreeFox - Free PDF & Image Tools Online",
    description:
      "Free online PDF and image tools. Compress PDFs, merge PDFs, convert images, resize photos, and more. No registration required.",
    images: [
      {
        url: "/og-image.png",
        width: 1200,
        height: 630,
        alt: "FreeFox - Free PDF & Image Tools",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "FreeFox - Free PDF & Image Tools Online",
    description:
      "Free online PDF and image tools. Compress PDFs, merge PDFs, convert images, resize photos, and more.",
    images: ["/twitter-image.png"],
    creator: "@freefox",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  verification: {
    google: "your-google-verification-code",
    yandex: "your-yandex-verification-code",
    yahoo: "your-yahoo-verification-code",
  },
  alternates: {
    canonical: "https://freefox.com",
  },
  category: "technology",
};

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 5,
  userScalable: true,
  themeColor: [
    { media: "(prefers-color-scheme: light)", color: "#ffffff" },
    { media: "(prefers-color-scheme: dark)", color: "#000000" },
  ],
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className={`${sora.variable} font-sans`}>
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link
          rel="preconnect"
          href="https://fonts.gstatic.com"
          crossOrigin="anonymous"
        />
        <link rel="dns-prefetch" href="https://fonts.googleapis.com" />
        <link rel="dns-prefetch" href="https://fonts.gstatic.com" />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} ${poppins.variable} ${sora.variable} font-sans antialiased`}
        style={{
          fontFamily: "var(--font-sora), ui-sans-serif, system-ui, sans-serif",
        }}
      >
        <Toaster />
        {children}
      </body>
    </html>
  );
}
