"use client";

import { MainLayout } from "@/components/MainLayout";
import { motion } from "framer-motion";
import Link from "next/link";

export default function PrivacyPage() {
  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { type: "spring", stiffness: 500, damping: 30 },
    },
  };

  return (
    <MainLayout>
      <motion.div
        className="max-w-3xl mx-auto py-8"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        <motion.div className="mb-8" variants={itemVariants}>
          <h1 className="text-3xl font-bold font-display mb-2">
            Privacy Policy
          </h1>
          <p className="text-muted-foreground">
            Last updated:{" "}
            {new Date().toLocaleDateString("en-US", {
              month: "long",
              day: "numeric",
              year: "numeric",
            })}
          </p>
        </motion.div>

        <motion.div
          className="prose prose-stone dark:prose-invert max-w-none space-y-6"
          variants={itemVariants}
        >
          <section className="space-y-4">
            <h2 className="text-xl font-semibold font-display">
              1. Introduction
            </h2>
            <p className="text-muted-foreground">
              At FreeFox, we respect your privacy and are committed to
              protecting your personal data. This Privacy Policy explains how we
              collect, use, and safeguard your information when you use our
              services.
            </p>
            <p className="text-muted-foreground">
              We do not collect personal information unless explicitly provided
              by you. We do not sell, trade, or rent your personal information
              to third parties.
            </p>
          </section>

          <section className="space-y-4">
            <h2 className="text-xl font-semibold font-display">
              2. Information We Collect
            </h2>
            <p className="text-muted-foreground">
              We collect minimal information to provide and improve our
              services:
            </p>
            <ul className="list-disc pl-6 text-muted-foreground space-y-2">
              <li>
                <strong>Usage Data:</strong> Anonymous information about how you
                use our services, including browser type, referring/exit pages,
                and date/time stamps.
              </li>
              <li>
                <strong>Cookies:</strong> Small files stored on your device to
                enhance your experience. You can set your browser to refuse
                cookies, but some parts of our service may not function
                properly.
              </li>
              <li>
                <strong>Files You Upload:</strong> We process the files you
                upload to use our services, but we do not store them
                permanently. All files are automatically deleted once processing
                is complete.
              </li>
            </ul>
          </section>

          <section className="space-y-4">
            <h2 className="text-xl font-semibold font-display">
              3. How We Use Your Information
            </h2>
            <p className="text-muted-foreground">
              We use the information we collect to:
            </p>
            <ul className="list-disc pl-6 text-muted-foreground space-y-2">
              <li>Provide, maintain, and improve our services</li>
              <li>Understand how users interact with our services</li>
              <li>Detect, prevent, and address technical issues</li>
              <li>Monitor the usage of our services</li>
            </ul>
          </section>

          <section className="space-y-4">
            <h2 className="text-xl font-semibold font-display">
              4. Data Security
            </h2>
            <p className="text-muted-foreground">
              We implement appropriate security measures to protect your
              personal information from unauthorized access, alteration,
              disclosure, or destruction. However, no method of transmission
              over the Internet or electronic storage is 100% secure, and we
              cannot guarantee absolute security.
            </p>
            <p className="text-muted-foreground">
              Your files are processed on-the-fly and are not stored permanently
              on our servers. They are automatically deleted once processing is
              complete.
            </p>
          </section>

          <section className="space-y-4">
            <h2 className="text-xl font-semibold font-display">
              5. Third-Party Services
            </h2>
            <p className="text-muted-foreground">
              We may use third-party services to help us operate our service or
              administer activities on our behalf, such as analytics or error
              tracking. These third parties have access to your information only
              to perform these tasks on our behalf and are obligated not to
              disclose or use it for any other purpose.
            </p>
          </section>

          <section className="space-y-4">
            <h2 className="text-xl font-semibold font-display">
              6. Children&apos;s Privacy
            </h2>
            <p className="text-muted-foreground">
              Our services are not intended for use by children under the age of
              13. We do not knowingly collect personal information from children
              under 13. If you are a parent or guardian and you are aware that
              your child has provided us with personal information, please
              contact us.
            </p>
          </section>

          <section className="space-y-4">
            <h2 className="text-xl font-semibold font-display">
              7. Changes to This Privacy Policy
            </h2>
            <p className="text-muted-foreground">
              We may update our Privacy Policy from time to time. We will notify
              you of any changes by posting the new Privacy Policy on this page
              and updating the &quot;Last updated&quot; date.
            </p>
            <p className="text-muted-foreground">
              You are advised to review this Privacy Policy periodically for any
              changes. Changes to this Privacy Policy are effective when they
              are posted on this page.
            </p>
          </section>

          <section className="space-y-4">
            <h2 className="text-xl font-semibold font-display">
              8. Contact Us
            </h2>
            <p className="text-muted-foreground">
              If you have any questions about this Privacy Policy, please
              contact us at{" "}
              <a
                href={`mailto:${
                  process.env.NEXT_PUBLIC_CONTACT_EMAIL || "<EMAIL>"
                }`}
                className="text-primary hover:underline"
              >
                {process.env.NEXT_PUBLIC_CONTACT_EMAIL || "<EMAIL>"}
              </a>
              .
            </p>
          </section>
        </motion.div>

        <motion.div className="mt-12 pt-6 border-t" variants={itemVariants}>
          <Link
            href="/"
            className="text-primary hover:underline flex items-center gap-2"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="h-4 w-4"
            >
              <path d="m12 19-7-7 7-7" />
              <path d="M19 12H5" />
            </svg>
            Back to Home
          </Link>
        </motion.div>
      </motion.div>
    </MainLayout>
  );
}
