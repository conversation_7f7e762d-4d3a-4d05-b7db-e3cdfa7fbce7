"use client";

import { EnhancedFileUpload } from "@/components/EnhancedFileUpload";
import { MainLayout } from "@/components/MainLayout";
import { SEOFaq } from "@/components/SEOFaq";
import { SuccessConfetti } from "@/components/SuccessConfetti";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { apiPost, downloadFromResponse } from "@/lib/api";
import { motion } from "framer-motion";
import { useEffect, useState } from "react";
import { toast } from "sonner";

const imageConvertFAQs = [
  {
    question: "What image formats can I convert between?",
    answer:
      "You can convert between JPG, PNG, WebP, GIF, BMP, and TIFF formats. Each format has unique advantages for different use cases.",
  },
  {
    question: "Will converting affect image quality?",
    answer:
      "Quality depends on the target format. Converting to JPG may reduce quality due to compression, while PNG and TIFF maintain original quality.",
  },
  {
    question: "Which format should I choose?",
    answer:
      "JPG for photos (smaller files), PNG for graphics with transparency, WebP for web optimization, GIF for animations, and TIFF for professional printing.",
  },
  {
    question: "Can I convert multiple images at once?",
    answer:
      "Currently, you can convert one image at a time. For batch conversion, please convert each image individually.",
  },
  {
    question: "Are there any file size limits?",
    answer:
      "No, there are no file size limits. You can convert large high-resolution images without restrictions.",
  },
];

const jsonLd = {
  "@context": "https://schema.org",
  "@type": "WebApplication",
  name: "Convert Image",
  description:
    "Free online image converter to convert between JPG, PNG, WebP, GIF, BMP, and TIFF formats",
  url: "https://freefox.com/convert-image",
  applicationCategory: "UtilitiesApplication",
  operatingSystem: "Any",
  offers: {
    "@type": "Offer",
    price: "0",
    priceCurrency: "USD",
  },
  featureList: [
    "Convert JPG to PNG",
    "Convert PNG to JPG",
    "Convert to WebP",
    "Multiple format support",
  ],
};

export default function ConvertImagePage() {
  const [files, setFiles] = useState<File[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [showConfetti, setShowConfetti] = useState(false);
  const [targetFormat, setTargetFormat] = useState("png");

  useEffect(() => {
    document.title =
      "Free Image Converter Online - Convert JPG PNG WebP | FreeFox";
    const metaDescription = document.querySelector('meta[name="description"]');
    if (metaDescription) {
      metaDescription.setAttribute(
        "content",
        "Convert images between formats online for free. Support for JPG, PNG, WebP, GIF, BMP, TIFF. No registration required. Fast and secure conversion."
      );
    }
    let canonical = document.querySelector('link[rel="canonical"]');
    if (!canonical) {
      canonical = document.createElement("link");
      canonical.setAttribute("rel", "canonical");
      document.head.appendChild(canonical);
    }
    canonical.setAttribute("href", "https://freefox.com/convert-image");
  }, []);

  const handleFilesSelected = (selectedFiles: File[]) => {
    // With replaceExisting=true, we'll always get just the latest file
    setFiles(selectedFiles);
  };

  const handleConvert = async () => {
    if (files.length === 0) {
      toast.error("Please select an image file to convert");
      return;
    }

    setIsProcessing(true);
    setProgress(10);

    try {
      // Create a FormData object to send the file
      const formData = new FormData();
      formData.append("file", files[0]);
      formData.append("target_format", targetFormat);

      // Simulate progress
      const progressInterval = setInterval(() => {
        setProgress((prev) => {
          if (prev >= 80) {
            clearInterval(progressInterval);
            return prev;
          }
          return prev + 5;
        });
      }, 300);

      setProgress(30);

      // Use the API utility instead of direct fetch
      const response = await apiPost("convert-image", formData);

      setProgress(90);

      // Get the original filename without extension
      const originalName =
        files[0].name.substring(0, files[0].name.lastIndexOf(".")) ||
        files[0].name;

      // Use the utility function to handle the download
      await downloadFromResponse(response, `${originalName}.${targetFormat}`);

      setProgress(100);
      setShowConfetti(true);
      toast.success(
        `Image converted to ${targetFormat.toUpperCase()} successfully`
      );
    } catch (error) {
      console.error("Error converting image:", error);
      toast.error("Failed to convert image. Please try again.");
    } finally {
      setTimeout(() => {
        setIsProcessing(false);
        setProgress(0);
      }, 500);
    }
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { type: "spring", stiffness: 400, damping: 30 },
    },
  };

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />
      <MainLayout>
        <SuccessConfetti
          show={showConfetti}
          duration={3000}
          onComplete={() => setShowConfetti(false)}
        />

        <motion.div
          className="max-w-3xl mx-auto space-y-8"
          initial="hidden"
          animate="visible"
          variants={containerVariants}
        >
          <motion.div className="text-center space-y-4" variants={itemVariants}>
            <div className="space-y-2">
              <h1 className="text-4xl font-bold tracking-tight">
                Free Image Converter Online
              </h1>
              <p className="text-xl text-muted-foreground">
                Convert between JPG, PNG, WebP, GIF, BMP, and TIFF formats. No
                registration required.
              </p>
            </div>
            <div className="flex flex-wrap gap-2 text-sm text-muted-foreground justify-center">
              <span className="bg-primary/10 px-2 py-1 rounded">
                ✓ Multiple Formats
              </span>
              <span className="bg-primary/10 px-2 py-1 rounded">
                ✓ High Quality
              </span>
              <span className="bg-primary/10 px-2 py-1 rounded">
                ✓ Fast Conversion
              </span>
              <span className="bg-primary/10 px-2 py-1 rounded">
                ✓ Instant Download
              </span>
            </div>
          </motion.div>

          <motion.div className="space-y-6" variants={itemVariants}>
            <EnhancedFileUpload
              acceptedFileTypes={{
                "image/jpeg": [".jpg", ".jpeg"],
                "image/png": [".png"],
                "image/webp": [".webp"],
                "image/gif": [".gif"],
                "image/bmp": [".bmp"],
                "image/tiff": [".tiff", ".tif"],
              }}
              maxFiles={1}
              onFilesSelected={handleFilesSelected}
              isProcessing={isProcessing}
              processingProgress={progress}
              replaceExisting={true}
            />

            {files.length > 0 && (
              <div className="bg-muted/30 p-4 rounded-lg space-y-4">
                <h3 className="font-medium font-display">Conversion Options</h3>
                <div className="space-y-2">
                  <div className="flex flex-col space-y-1">
                    <label className="text-sm font-medium">Target Format</label>
                    <Select
                      value={targetFormat}
                      onValueChange={setTargetFormat}
                      disabled={isProcessing}
                    >
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder="Select format" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="jpg">JPG</SelectItem>
                        <SelectItem value="png">PNG</SelectItem>
                        <SelectItem value="webp">WebP</SelectItem>
                        <SelectItem value="gif">GIF</SelectItem>
                        <SelectItem value="bmp">BMP</SelectItem>
                        <SelectItem value="tiff">TIFF</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>
            )}

            {files.length > 0 && (
              <div className="flex justify-end">
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Button
                    onClick={handleConvert}
                    disabled={isProcessing}
                    className="px-8"
                  >
                    {isProcessing ? "Converting..." : "Convert Image"}
                  </Button>
                </motion.div>
              </div>
            )}
          </motion.div>

          <motion.div
            className="bg-muted/30 p-6 rounded-lg space-y-4"
            variants={itemVariants}
          >
            <h2 className="text-xl font-bold font-display">
              About Image Conversion
            </h2>
            <div className="space-y-2 text-sm text-muted-foreground">
              <p>
                Our image converter allows you to convert images between
                different formats. Each format has its own advantages:
              </p>
              <ul className="list-disc list-inside space-y-1">
                <li>
                  <strong>JPG</strong>: Best for photographs, smaller file size
                  but lossy compression
                </li>
                <li>
                  <strong>PNG</strong>: Supports transparency, lossless
                  compression, good for graphics
                </li>
                <li>
                  <strong>WebP</strong>: Modern format with excellent
                  compression, supports transparency
                </li>
                <li>
                  <strong>GIF</strong>: Supports animation, limited color
                  palette
                </li>
                <li>
                  <strong>BMP</strong>: Uncompressed format, large file size but
                  no quality loss
                </li>
                <li>
                  <strong>TIFF</strong>: High-quality format used in
                  professional photography and printing
                </li>
              </ul>
              <p>
                Choose the format that best suits your needs based on quality,
                file size, and compatibility requirements.
              </p>
            </div>
          </motion.div>

          <motion.div variants={itemVariants}>
            <SEOFaq
              title="Image Conversion FAQ"
              faqs={imageConvertFAQs}
              toolName="Image Converter"
            />
          </motion.div>
        </motion.div>
      </MainLayout>
    </>
  );
}
