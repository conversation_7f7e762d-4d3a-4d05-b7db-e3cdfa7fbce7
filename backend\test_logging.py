#!/usr/bin/env python3
"""
Test script to demonstrate the error logging system

This script shows how the logging system works:
1. Creates sample log entries
2. Demonstrates error logging with context
3. Shows log rotation and cleanup
"""

import asyncio
import sys
import time
from pathlib import Path

# Add the app directory to the path
sys.path.insert(0, str(Path(__file__).parent / "app"))

from app.middleware.logging_middleware import request_context
from app.utils.error_logger import Operation, log_error, log_info, log_warning


def test_basic_logging():
    """Test basic logging functionality"""
    print("🧪 Testing basic logging functionality...\n")

    # Test info logging (console only) - icon added automatically
    log_info("This is an info message - appears in console only")

    # Test warning logging (console only) - icon added automatically
    log_warning("This is a warning message - appears in console only")

    # Test error logging (both console and file) - icon added automatically
    log_error("This is an error message - appears in both console and file")

    print("\n" + "="*60 + "\n")


def test_error_with_exception():
    """Test error logging with exception details"""
    print("🧪 Testing error logging with exception...\n")

    try:
        # Simulate an error
        _ = 10 / 0
    except Exception as e:
        log_error(
            "Division by zero error occurred",
            exc=e,
            extra_data={
                'operation': 'division',
                'numerator': 10,
                'denominator': 0
            },
            operation=Operation.FAILURE
        )

    print("\n" + "="*60 + "\n")


def test_operation_specific_icons():
    """Test operation-specific icons"""
    print("🧪 Testing operation-specific icons...\n")

    # Test various operations with specific icons
    log_info("API starting up", operation=Operation.STARTUP)
    log_info("File uploaded successfully", operation=Operation.FILE_UPLOAD)
    log_info("PDF compression completed", operation=Operation.PDF_COMPRESS)
    log_info("Image resizing finished", operation=Operation.IMAGE_RESIZE)
    log_warning("Memory usage is high", operation=Operation.MEMORY_WARNING)
    log_error("File not found", operation=Operation.FILE_NOT_FOUND)
    log_error("Permission denied", operation=Operation.PERMISSION_ERROR)
    log_info("Cleanup completed", operation=Operation.CLEANUP)
    log_info("API shutting down", operation=Operation.SHUTDOWN)

    print("\n" + "="*60 + "\n")


def test_request_context_logging():
    """Test logging with request context"""
    print("🧪 Testing logging with request context...\n")

    # Simulate request context
    context = {
        'request_id': 'test-123',
        'method': 'POST',
        'endpoint': '/api/v1/test-endpoint',
        'user_agent': 'Test-Agent/1.0',
        'client_ip': '127.0.0.1'
    }

    # Set context
    request_context.set(context)

    try:
        # Simulate an error with context
        raise ValueError("Simulated validation error")
    except Exception as e:
        log_error(
            "Request processing failed",
            exc=e,
            request_context=context,
            extra_data={
                'validation_field': 'email',
                'provided_value': 'invalid-email'
            },
            operation=Operation.VALIDATION_ERROR
        )

    print("\n" + "="*60 + "\n")


def test_multiple_errors():
    """Test multiple error scenarios"""
    print("🧪 Testing multiple error scenarios...\n")

    scenarios = [
        ("File not found", FileNotFoundError("test.pdf not found")),
        ("Permission denied", PermissionError("Access denied to /tmp/file.pdf")),
        ("Invalid format", ValueError("Invalid PDF format")),
        ("Network timeout", TimeoutError("Request timed out after 30s")),
        ("Memory error", MemoryError("Not enough memory to process large file"))
    ]

    for i, (description, exception) in enumerate(scenarios, 1):
        log_error(
            f"Error scenario {i}: {description}",
            exc=exception,
            extra_data={
                'scenario_id': i,
                'error_type': type(exception).__name__
            },
            operation=Operation.FAILURE
        )
        time.sleep(0.1)  # Small delay to show different timestamps

    print("\n" + "="*60 + "\n")


def test_log_file_creation():
    """Test that log files are created correctly"""
    print("🧪 Testing log file creation...\n")

    # Check if logs directory exists
    logs_dir = Path("logs")
    if logs_dir.exists():
        log_info(f"📁 Logs directory exists: {logs_dir.absolute()}")

        # List log files
        log_files = list(logs_dir.glob("*.log"))
        if log_files:
            log_info(f"📄 Found {len(log_files)} log file(s):")
            for log_file in log_files:
                size = log_file.stat().st_size
                log_info(f"   - {log_file.name} ({size} bytes)")
        else:
            log_info("📄 No log files found yet")
    else:
        log_info("📁 Logs directory will be created when first error is logged")

    print("\n" + "="*60 + "\n")


def demonstrate_eye_soothing_output():
    """Demonstrate the eye-soothing colored output"""
    print("🧪 Demonstrating eye-soothing colored output...\n")

    log_info("This is a beautiful info message with colors!", operation=Operation.SUCCESS)
    log_warning("This is a warning with pleasant yellow colors", operation=Operation.MEMORY_WARNING)
    log_error("This is an error with readable red colors", operation=Operation.FAILURE)

    # Error with exception
    try:
        raise RuntimeError("This is a sample exception for demonstration")
    except Exception as e:
        log_error("Error with exception traceback (beautifully formatted)", exc=e, operation=Operation.FAILURE)

    print("\n" + "="*60 + "\n")


async def main():
    """Main test function"""
    print("🚀 FreeFox Error Logging System Test\n")
    print("="*60)
    print("This test demonstrates the enhanced error logging system")
    print("with daily rotation, cleanup, and eye-soothing output.")
    print("="*60 + "\n")

    # Run all tests
    test_basic_logging()
    test_operation_specific_icons()
    test_error_with_exception()
    test_request_context_logging()
    test_multiple_errors()
    test_log_file_creation()
    demonstrate_eye_soothing_output()

    print("✅ All logging tests completed!")
    print("\n📋 Summary:")
    print("- Info and warning messages appear in console only")
    print("- Error messages appear in both console and log files")
    print("- Log files are rotated daily and cleaned up after 7 days")
    print("- Console output uses eye-soothing colors")
    print("- Error logs are in structured JSON format for easy parsing")
    print("\n🔍 Check the 'logs/' directory for error log files")


if __name__ == "__main__":
    asyncio.run(main())
