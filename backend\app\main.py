from app.middleware import LoggingMiddleware
from app.routes import image_tools, pdf_tools
from app.utils.error_logger import Operation, log_error, log_info, log_warning
from fastapi import <PERSON><PERSON><PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

# Create FastAPI app with configuration for handling large files
app = FastAPI(
    title="FreeFox API",
    description="API for PDF utilities including merge, compress, and Word to PDF conversion",
    version="1.0.0",
)

# Add logging middleware (must be added before CORS)
app.add_middleware(LoggingMiddleware, log_requests=True)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_methods=["*"],
    allow_headers=["*"],
    expose_headers=[
        "Content-Disposition",
        "X-Compression-Ratio",
        "X-Original-Size",
        "X-Compressed-Size",
        "X-Process-Time",
        "X-Request-ID"
    ],
)

# Global error handling
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    # Log the error with context
    log_error(
        f"Unhandled exception in global handler: {str(exc)}",
        exc=exc,
        request_context={
            'method': request.method,
            'endpoint': str(request.url.path),
            'query_params': str(request.query_params) if request.query_params else None,
        },
        operation=Operation.FAILURE
    )

    return JSONResponse(
        status_code=500,
        content={"detail": f"An unexpected error occurred: {str(exc)}"},
    )

# Startup and shutdown events
@app.on_event("startup")
async def startup_event():
    log_info("FreeFox API starting up...", operation=Operation.STARTUP)
    log_info("Error logs will be saved to: logs/errors_YYYY-MM-DD.log")
    log_info("Log rotation: Daily, keeping 7 days of history")

@app.on_event("shutdown")
async def shutdown_event():
    log_info("FreeFox API shutting down...", operation=Operation.SHUTDOWN)

# Include routers
app.include_router(pdf_tools.router, prefix="/api/v1", tags=["PDF Tools"])
app.include_router(image_tools.router, prefix="/api/v1", tags=["Image Tools"])

# Root endpoint
@app.get("/", tags=["Root"])
async def root():
    return {
        "message": "Welcome to FreeFox API",
        "docs": "/docs",
        "endpoints": {
            # PDF Tools
            "merge_pdf": "/api/v1/merge-pdf",
            "compress_pdf": "/api/v1/compress-pdf",
            "word_to_pdf": "/api/v1/word-to-pdf",
            "split_pdf": "/api/v1/split-pdf",
            "rotate_pdf": "/api/v1/rotate-pdf",
            "pdf_to_word": "/api/v1/pdf-to-word",

            # Image Tools
            "compress_image": "/api/v1/compress-image",
            "convert_image": "/api/v1/convert-image",
            "resize_image": "/api/v1/resize-image",
            "crop_image": "/api/v1/crop-image",
            "upscale_image": "/api/v1/upscale-image",
            "image_to_pdf": "/api/v1/image-to-pdf",
        },
    }
