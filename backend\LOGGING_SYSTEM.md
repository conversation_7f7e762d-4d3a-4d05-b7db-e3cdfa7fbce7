# FreeFox Backend Error Logging System

## Overview

The FreeFox backend now includes a comprehensive error logging system with the following features:

- **Daily log rotation** with unique date-based filenames
- **Automatic cleanup** of logs older than 7 days
- **Eye-soothing colored console output** for better readability
- **File-only error logging** (no info/debug clutter in files)
- **Structured JSON format** for easy parsing and analysis
- **Request context tracking** throughout the request lifecycle
- **Dual output**: Console for development, files for production monitoring

## Features

### 🎨 Eye-Soothing Console Output

- **Color-coded log levels**: Info (green), Warning (yellow), Error (red)
- **Automatic icons**: Icons are added automatically based on log level and operation
- **Readable timestamps**: HH:MM:SS format
- **Clean formatting**: Pipe-separated columns for easy scanning
- **Exception highlighting**: Red-bordered exception tracebacks
- **Operation-specific icons**: Different icons for different operations (PDF, image, network, etc.)

### 📁 File Logging

- **Error-only**: Only errors are written to files (keeps files focused)
- **JSON format**: Structured data for easy parsing
- **Daily rotation**: New file each day with format `errors_YYYY-MM-DD.log`
- **Auto cleanup**: Files older than 7 days are automatically removed
- **Request context**: Full request information included in error logs

### 🔄 Request Tracking

- **Unique request IDs**: Each request gets a unique 8-character ID
- **Context propagation**: Request context available throughout the request
- **Performance tracking**: Request processing time included
- **Client information**: IP, User-Agent, and other headers captured

## File Structure

```
backend/
├── app/
│   ├── utils/
│   │   └── error_logger.py          # Core logging functionality
│   ├── middleware/
│   │   ├── __init__.py
│   │   └── logging_middleware.py    # Request context middleware
│   └── main.py                      # Updated with logging integration
├── logs/                            # Created automatically
│   ├── errors_2024-01-15.log      # Today's errors
│   ├── errors_2024-01-14.log      # Yesterday's errors
│   └── ...                         # Older files (auto-cleaned after 7 days)
├── test_logging.py                 # Test script
└── LOGGING_SYSTEM.md              # This documentation
```

## Usage Examples

### Basic Error Logging

```python
from app.utils.error_logger import log_error, log_info, log_warning

# Info logging (console only) - icon added automatically
log_info("Service started successfully")

# Warning logging (console only) - icon added automatically
log_warning("High memory usage detected")

# Error logging (console + file) - icon added automatically
log_error("Database connection failed")
```

### Operation-Specific Logging

```python
from app.utils.error_logger import Operation

# Use Operation enum for specific icons
log_info("API starting up", operation=Operation.STARTUP)  # 🚀
log_info("File uploaded", operation=Operation.FILE_UPLOAD)  # 📤
log_info("PDF compressed", operation=Operation.PDF_COMPRESS)  # 📄🗜️
log_warning("Memory high", operation=Operation.MEMORY_WARNING)  # 🧠⚠️
log_error("File not found", operation=Operation.FILE_NOT_FOUND)  # 📄❓

# Also supports legacy string format for backward compatibility
log_info("API starting up", operation="startup")  # Still works
```

### Error with Exception

```python
try:
    result = risky_operation()
except Exception as e:
    log_error(
        "Operation failed",
        exc=e,  # Include full exception details
        extra_data={
            'operation': 'pdf_compression',
            'file_size': 1024000,
            'user_id': 'user123'
        },
        operation=Operation.PDF_COMPRESS  # Adds 📄🗜️ icon automatically
    )
```

### Request Context Logging

```python
from app.middleware import log_error_with_context, add_context_data

# Add extra context data
add_context_data('operation', 'pdf_merge')
add_context_data('file_count', 3)

# Log error with full request context (icon added automatically)
log_error_with_context(
    "PDF merge failed",
    exc=exception,
    extra_data={'total_size': 5000000, 'operation': 'pdf_merge'}
)
```

## Log File Format

Error logs are stored in JSON format for easy parsing:

```json
{
  "timestamp": "2024-01-15T14:30:25.123456",
  "level": "ERROR",
  "message": "📄🗜️ PDF compression failed",
  "module": "pdf_tools",
  "function": "compress_pdf",
  "line": 220,
  "thread": 140234567890,
  "process": 12345,
  "request_id": "abc12345",
  "endpoint": "/api/v1/compress-pdf",
  "method": "POST",
  "user_agent": "Mozilla/5.0...",
  "exception": {
    "type": "ValueError",
    "message": "Invalid compression quality",
    "traceback": ["Traceback...", "..."]
  },
  "extra": {
    "operation": "pdf_compression",
    "file_size": 1024000,
    "quality": 150
  }
}
```

## Configuration

### Log Directory

Default: `logs/` (created automatically)

### Retention Period

Default: 7 days (configurable in `error_logger.py`)

### Log Levels

- **Console**: INFO and above
- **File**: ERROR and above only

## Testing

Run the test script to see the logging system in action:

```bash
cd backend
python test_logging.py
```

This will:

- Create sample log entries
- Demonstrate colored console output
- Show error logging with context
- Create log files in the `logs/` directory

## Integration

The logging system is automatically integrated into:

1. **FastAPI middleware**: Captures all request/response information
2. **Global exception handler**: Logs unhandled exceptions
3. **Route handlers**: Enhanced error logging in PDF/image tools
4. **Startup/shutdown events**: Service lifecycle logging

## Best Practices

### 1. Use Appropriate Log Levels

- `log_info()`: General information (console only)
- `log_warning()`: Potential issues (console only)
- `log_error()`: Actual errors (console + file)

### 2. Include Context

Always include relevant context in error logs:

```python
log_error(
    "File processing failed",
    exc=exception,
    extra_data={
        'filename': file.filename,
        'file_size': file.size,
        'operation': 'compression'
    }
)
```

### 3. Use Operation Enum for Automatic Icons

Icons are added automatically based on log level and operation:

```python
from app.utils.error_logger import Operation

# Use Operation enum constants (recommended)
log_info("API starting up", operation=Operation.STARTUP)  # 🚀
log_info("PDF compressed", operation=Operation.PDF_COMPRESS)  # 📄🗜️
log_info("File uploaded", operation=Operation.FILE_UPLOAD)  # 📤
log_error("Operation failed", operation=Operation.FAILURE)  # 💥

# Legacy string format still supported
log_info("API starting up", operation="startup")  # Still works
```

**Available Operation Constants:**

- **General**: `STARTUP`, `SHUTDOWN`, `SUCCESS`, `FAILURE`, `PROCESSING`, `COMPLETED`
- **Files**: `FILE_UPLOAD`, `FILE_DOWNLOAD`, `FILE_SAVE`, `FILE_DELETE`, `FILE_NOT_FOUND`, `FILE_CORRUPTED`
- **PDF**: `PDF_MERGE`, `PDF_SPLIT`, `PDF_COMPRESS`, `PDF_ROTATE`, `PDF_CONVERT`
- **Images**: `IMAGE_COMPRESS`, `IMAGE_RESIZE`, `IMAGE_CONVERT`, `IMAGE_CROP`, `IMAGE_UPSCALE`
- **Network**: `REQUEST_RECEIVED`, `RESPONSE_SENT`, `TIMEOUT`, `CONNECTION_ERROR`
- **System**: `MEMORY_WARNING`, `DISK_WARNING`, `CLEANUP`, `VALIDATION_ERROR`, `PERMISSION_ERROR`

### 4. Structured Extra Data

Use dictionaries for extra data to maintain JSON structure:

```python
# Good
extra_data={'user_id': 123, 'action': 'upload'}

# Avoid
extra_data="user_id=123 action=upload"
```

## Monitoring

### Log Analysis

Use tools like `jq` to analyze JSON logs:

```bash
# Count errors by type
cat logs/errors_*.log | jq -r '.exception.type' | sort | uniq -c

# Find errors for specific endpoint
cat logs/errors_*.log | jq 'select(.endpoint == "/api/v1/compress-pdf")'

# Get error summary
cat logs/errors_*.log | jq -r '"\(.timestamp) \(.message)"'
```

### Log Rotation Status

The system automatically:

- Creates new log files daily at midnight
- Removes files older than 7 days
- Prints cleanup messages to console

## Troubleshooting

### No Log Files Created

- Log files are only created when errors occur
- Check that the `logs/` directory is writable
- Run `python test_logging.py` to generate test logs

### Colors Not Showing

- Ensure `colorama` is installed: `pip install colorama`
- Colors may not work in some terminals (logs still function)

### Permission Issues

- Ensure the application has write permissions to the `logs/` directory
- Check file system permissions if cleanup fails

## Dependencies

- `colorama>=0.4.4`: For colored console output
- `fastapi`: For middleware integration
- `python>=3.7`: For modern Python features

The logging system is designed to be robust and fail gracefully if dependencies are missing.
