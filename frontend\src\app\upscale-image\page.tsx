"use client";

import { EnhancedFileUpload } from "@/components/EnhancedFileUpload";
import { MainLayout } from "@/components/MainLayout";
import { SEOFaq } from "@/components/SEOFaq";
import { SuccessConfetti } from "@/components/SuccessConfetti";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import { apiPost, downloadFromResponse } from "@/lib/api";
import { motion } from "framer-motion";
import { useEffect, useState } from "react";
import { toast } from "sonner";

const imageUpscaleFAQs = [
  {
    question: "What is image upscaling?",
    answer:
      "Image upscaling increases the resolution and size of images using advanced algorithms to add pixels while preserving quality and enhancing details.",
  },
  {
    question: "What image formats can I upscale?",
    answer:
      "You can upscale JPG, PNG, WebP, GIF, BMP, and TIFF images. All formats are supported with high-quality output.",
  },
  {
    question: "How much can I upscale an image?",
    answer:
      "You can upscale images from 1.0x to 4.0x their original size. Higher scale factors result in larger images but may take longer to process.",
  },
  {
    question: "Will upscaling improve image quality?",
    answer:
      "Upscaling can enhance image quality by using intelligent algorithms to add detail, but it cannot create information that wasn't in the original image.",
  },
  {
    question: "How long does upscaling take?",
    answer:
      "Processing time depends on the original image size and scale factor. Larger images and higher scale factors take longer to process.",
  },
];

const jsonLd = {
  "@context": "https://schema.org",
  "@type": "WebApplication",
  name: "Upscale Image",
  description:
    "Free online image upscaler to increase resolution and enhance image quality",
  url: "https://freefox.com/upscale-image",
  applicationCategory: "UtilitiesApplication",
  operatingSystem: "Any",
  offers: {
    "@type": "Offer",
    price: "0",
    priceCurrency: "USD",
  },
  featureList: [
    "Upscale images",
    "Enhance quality",
    "Multiple scale factors",
    "Advanced algorithms",
  ],
};

export default function UpscaleImagePage() {
  const [files, setFiles] = useState<File[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [showConfetti, setShowConfetti] = useState(false);
  const [scaleFactor, setScaleFactor] = useState(2.0);
  const [enhanceQuality, setEnhanceQuality] = useState(true);

  useEffect(() => {
    document.title =
      "Free Image Upscaler Online - Enhance Image Resolution | FreeFox";
    const metaDescription = document.querySelector('meta[name="description"]');
    if (metaDescription) {
      metaDescription.setAttribute(
        "content",
        "Upscale images online for free. Increase resolution and enhance quality using advanced algorithms. Support for JPG, PNG, WebP and more. No registration required."
      );
    }
    let canonical = document.querySelector('link[rel="canonical"]');
    if (!canonical) {
      canonical = document.createElement("link");
      canonical.setAttribute("rel", "canonical");
      document.head.appendChild(canonical);
    }
    canonical.setAttribute("href", "https://freefox.com/upscale-image");
  }, []);

  const handleFilesSelected = (selectedFiles: File[]) => {
    // With replaceExisting=true, we'll always get just the latest file
    setFiles(selectedFiles);
  };

  const handleUpscale = async () => {
    if (files.length === 0) {
      toast.error("Please select an image file to upscale");
      return;
    }

    setIsProcessing(true);
    setProgress(10);

    try {
      // Create a FormData object to send the file
      const formData = new FormData();
      formData.append("file", files[0]);
      formData.append("scale_factor", scaleFactor.toString());
      formData.append("enhance_quality", enhanceQuality.toString());

      // Simulate progress
      const progressInterval = setInterval(() => {
        setProgress((prev) => {
          if (prev >= 80) {
            clearInterval(progressInterval);
            return prev;
          }
          return prev + 3; // Slower progress for upscaling which takes longer
        });
      }, 300);

      setProgress(30);

      // Use the API utility instead of direct fetch
      const response = await apiPost("upscale-image", formData);

      setProgress(90);

      // Use the utility function to handle the download
      await downloadFromResponse(response, `upscaled_${files[0].name}`);

      setProgress(100);
      setShowConfetti(true);
      toast.success("Image upscaled successfully");
    } catch (error) {
      console.error("Error upscaling image:", error);
      toast.error("Failed to upscale image. Please try again.");
    } finally {
      setTimeout(() => {
        setIsProcessing(false);
        setProgress(0);
      }, 500);
    }
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { type: "spring", stiffness: 400, damping: 30 },
    },
  };

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />
      <MainLayout>
        <SuccessConfetti
          show={showConfetti}
          duration={3000}
          onComplete={() => setShowConfetti(false)}
        />

        <motion.div
          className="max-w-3xl mx-auto space-y-8"
          initial="hidden"
          animate="visible"
          variants={containerVariants}
        >
          <motion.div className="text-center space-y-4" variants={itemVariants}>
            <div className="space-y-2">
              <h1 className="text-4xl font-bold tracking-tight">
                Free Image Upscaler Online
              </h1>
              <p className="text-xl text-muted-foreground">
                Increase image resolution and enhance quality using advanced
                algorithms. No registration required.
              </p>
            </div>
            <div className="flex flex-wrap gap-2 text-sm text-muted-foreground justify-center">
              <span className="bg-primary/10 px-2 py-1 rounded">
                ✓ AI Enhancement
              </span>
              <span className="bg-primary/10 px-2 py-1 rounded">
                ✓ Up to 4x Scale
              </span>
              <span className="bg-primary/10 px-2 py-1 rounded">
                ✓ Quality Boost
              </span>
              <span className="bg-primary/10 px-2 py-1 rounded">
                ✓ Multiple Formats
              </span>
            </div>
          </motion.div>

          <motion.div className="space-y-6" variants={itemVariants}>
            <EnhancedFileUpload
              acceptedFileTypes={{
                "image/jpeg": [".jpg", ".jpeg"],
                "image/png": [".png"],
                "image/webp": [".webp"],
                "image/gif": [".gif"],
                "image/bmp": [".bmp"],
                "image/tiff": [".tiff", ".tif"],
              }}
              maxFiles={1}
              onFilesSelected={handleFilesSelected}
              isProcessing={isProcessing}
              processingProgress={progress}
              replaceExisting={true}
            />

            {files.length > 0 && (
              <div className="bg-muted/30 p-4 rounded-lg space-y-4">
                <h3 className="font-medium font-display">Upscaling Options</h3>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="text-sm">
                        Scale Factor: {scaleFactor.toFixed(1)}x
                      </span>
                      <span className="text-xs text-muted-foreground">
                        {scaleFactor < 2.0
                          ? "Smaller increase in size"
                          : scaleFactor < 3.0
                          ? "Balanced increase in size"
                          : "Larger increase in size"}
                      </span>
                    </div>
                    <Slider
                      value={[scaleFactor]}
                      min={1.0}
                      max={4.0}
                      step={0.1}
                      onValueChange={(value) => setScaleFactor(value[0])}
                      disabled={isProcessing}
                    />
                    <p className="text-xs text-muted-foreground mt-1">
                      Higher values result in larger images but may take longer
                      to process.
                    </p>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="enhance-quality"
                      checked={enhanceQuality}
                      onCheckedChange={(checked) =>
                        setEnhanceQuality(checked === true)
                      }
                      disabled={isProcessing}
                    />
                    <Label
                      htmlFor="enhance-quality"
                      className="text-sm font-normal"
                    >
                      Enhance image quality
                    </Label>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Applies additional enhancements to improve sharpness and
                    contrast.
                  </p>
                </div>
              </div>
            )}
            {files.length > 0 && (
              <div className="flex justify-end">
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Button
                    onClick={handleUpscale}
                    disabled={isProcessing}
                    className="px-8"
                  >
                    {isProcessing ? "Upscaling..." : "Upscale Image"}
                  </Button>
                </motion.div>
              </div>
            )}
          </motion.div>

          <motion.div variants={itemVariants}>
            <SEOFaq
              title="Image Upscaling FAQ"
              faqs={imageUpscaleFAQs}
              toolName="Image Upscaler"
            />
          </motion.div>
        </motion.div>
      </MainLayout>
    </>
  );
}
