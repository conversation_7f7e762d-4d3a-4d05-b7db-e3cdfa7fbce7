"use client";

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";

interface FAQItem {
  question: string;
  answer: string;
}

interface SEOFaqProps {
  title?: string;
  faqs: FAQItem[];
  toolName?: string;
}

export function SEOFaq({
  title = "Frequently Asked Questions",
  faqs,
}: SEOFaqProps) {
  const jsonLd = {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    mainEntity: faqs.map((faq) => ({
      "@type": "Question",
      name: faq.question,
      acceptedAnswer: {
        "@type": "Answer",
        text: faq.answer,
      },
    })),
  };

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />
      <div className="bg-muted/30 p-6 rounded-lg">
        <h2 className="text-2xl font-bold mb-6 text-center">{title}</h2>
        <Accordion type="single" collapsible className="w-full">
          {faqs.map((faq, index) => (
            <AccordionItem key={index} value={`item-${index}`}>
              <AccordionTrigger className="text-left">
                {faq.question}
              </AccordionTrigger>
              <AccordionContent className="text-muted-foreground">
                {faq.answer}
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
      </div>
    </>
  );
}

// Common FAQ data for different tools
export const pdfCompressionFAQs: FAQItem[] = [
  {
    question: "How much can I compress a PDF file?",
    answer:
      "Our PDF compressor can reduce file sizes by up to 90% depending on the content. Files with many images typically see larger reductions, while text-heavy documents may see smaller but still significant compression.",
  },
  {
    question: "Will compressing a PDF reduce its quality?",
    answer:
      "Our advanced compression algorithms are designed to maintain visual quality while reducing file size. We use lossless compression techniques for text and smart optimization for images to preserve readability and appearance.",
  },
  {
    question: "Is there a file size limit for PDF compression?",
    answer:
      "No, there are no file size limits. You can compress PDFs of any size, from small documents to large files with hundreds of pages.",
  },
  {
    question: "Is it safe to upload my PDF files?",
    answer:
      "Yes, absolutely. Your files are processed securely on our servers and are automatically deleted immediately after compression. We never store, access, or share your documents.",
  },
  {
    question: "Do I need to create an account to compress PDFs?",
    answer:
      "No registration is required. You can start compressing PDF files immediately without creating an account or providing any personal information.",
  },
];

export const pdfMergeFAQs: FAQItem[] = [
  {
    question: "How many PDF files can I merge at once?",
    answer:
      "You can merge up to 10 PDF files in a single operation. For larger batches, you can repeat the process multiple times.",
  },
  {
    question: "Will the merged PDF maintain the original quality?",
    answer:
      "Yes, our PDF merger preserves the original quality, formatting, and content of all your documents. The merged file will look exactly like the original files combined.",
  },
  {
    question: "Can I change the order of pages before merging?",
    answer:
      "Yes, you can drag and drop files in the upload area to arrange them in your desired order before merging.",
  },
  {
    question: "What happens to bookmarks and links in merged PDFs?",
    answer:
      "Bookmarks and internal links are preserved in the merged document. External links will continue to work as expected.",
  },
  {
    question: "Is there a limit to the total size of merged PDFs?",
    answer:
      "No, there are no limits on the total file size. You can merge large PDF files without restrictions.",
  },
];

export const imageCompressionFAQs: FAQItem[] = [
  {
    question: "What image formats can I compress?",
    answer:
      "You can compress JPEG, PNG, WebP, and other common image formats. Our tool automatically optimizes each format using the best compression techniques.",
  },
  {
    question: "How much can image file sizes be reduced?",
    answer:
      "Compression rates vary by image type and content. JPEG images can typically be reduced by 50-80%, while PNG images may see 20-60% reduction depending on complexity.",
  },
  {
    question: "Will compressing images affect their quality?",
    answer:
      "Our smart compression algorithms are designed to maintain visual quality while reducing file size. You can choose different compression levels based on your quality requirements.",
  },
  {
    question: "Can I compress multiple images at once?",
    answer:
      "Yes, you can upload and compress multiple images simultaneously. All compressed images will be packaged in a convenient ZIP file for download.",
  },
  {
    question: "Are my images stored on your servers?",
    answer:
      "No, your images are processed and immediately deleted from our servers. We prioritize your privacy and never store uploaded files.",
  },
];
